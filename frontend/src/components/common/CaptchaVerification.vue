<template>
  <div class="captcha-verification">
    <div class="card bg-base-100 border border-base-200/50 shadow-sm p-4">
      <div class="flex items-center gap-3">
        <div class="form-control">
          <label class="label cursor-pointer gap-3">
            <input 
              type="checkbox" 
              class="checkbox checkbox-primary" 
              v-model="isChecked"
              @change="handleVerification"
              :disabled="isVerifying"
            />
            <span class="label-text">{{ $t('contact.captcha.verify_human') }}</span>
          </label>
        </div>
        
        <!-- Loading state -->
        <div v-if="isVerifying" class="loading loading-spinner loading-sm text-primary"></div>
        
        <!-- Success state -->
        <div v-else-if="isVerified" class="flex items-center gap-1 text-success">
          <Icon name="check-circle" size="sm" />
          <span class="text-sm font-medium">{{ $t('contact.captcha.verified') }}</span>
        </div>
        
        <!-- Error state -->
        <div v-else-if="hasError" class="flex items-center gap-1 text-error">
          <Icon name="x-circle" size="sm" />
          <span class="text-sm">{{ $t('contact.captcha.error') }}</span>
        </div>
      </div>
      
      <!-- Math challenge for additional verification -->
      <div v-if="showMathChallenge" class="mt-4 p-3 bg-base-200/50 rounded-lg">
        <div class="flex items-center gap-3">
          <span class="text-sm font-medium">{{ $t('contact.captcha.solve') }}: {{ mathQuestion }}</span>
          <input 
            type="number" 
            v-model="mathAnswer"
            @input="checkMathAnswer"
            class="input input-bordered input-sm w-20"
            :placeholder="$t('contact.captcha.answer')"
          />
          <div v-if="mathCorrect" class="text-success">
            <Icon name="check" size="sm" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import Icon from '@/components/common/Icon.vue'

const { t } = useI18n()

// Props
interface Props {
  modelValue?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'verified': [token: string]
  'error': [error: string]
}>()

// State
const isChecked = ref(false)
const isVerifying = ref(false)
const isVerified = ref(false)
const hasError = ref(false)
const showMathChallenge = ref(false)
const mathAnswer = ref<number | null>(null)
const mathCorrect = ref(false)

// Math challenge
const num1 = ref(0)
const num2 = ref(0)
const mathQuestion = computed(() => `${num1.value} + ${num2.value} = ?`)
const correctAnswer = computed(() => num1.value + num2.value)

// Methods
const generateMathChallenge = () => {
  num1.value = Math.floor(Math.random() * 10) + 1
  num2.value = Math.floor(Math.random() * 10) + 1
  mathAnswer.value = null
  mathCorrect.value = false
}

const checkMathAnswer = () => {
  if (mathAnswer.value === correctAnswer.value) {
    mathCorrect.value = true
    completeVerification()
  } else {
    mathCorrect.value = false
  }
}

const handleVerification = async () => {
  if (!isChecked.value) {
    reset()
    return
  }

  isVerifying.value = true
  hasError.value = false

  try {
    // Simulate verification delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Show math challenge for additional security
    showMathChallenge.value = true
    generateMathChallenge()
    isVerifying.value = false
    
  } catch (error) {
    hasError.value = true
    isVerifying.value = false
    isChecked.value = false
    emit('error', 'Verification failed')
  }
}

const completeVerification = () => {
  isVerified.value = true
  showMathChallenge.value = false
  
  // Generate a simple verification token
  const token = `captcha_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  
  emit('update:modelValue', true)
  emit('verified', token)
}

const reset = () => {
  isVerified.value = false
  hasError.value = false
  showMathChallenge.value = false
  mathCorrect.value = false
  mathAnswer.value = null
  emit('update:modelValue', false)
}

// Watch for external changes
const updateFromParent = (value: boolean) => {
  if (!value) {
    reset()
    isChecked.value = false
  }
}

// Initialize
onMounted(() => {
  generateMathChallenge()
})

// Expose methods for parent component
defineExpose({
  reset,
  isVerified: computed(() => isVerified.value)
})
</script>

<style scoped>
.captcha-verification {
  user-select: none;
}

.checkbox:checked {
  animation: checkmark 0.3s ease-in-out;
}

@keyframes checkmark {
  0% { transform: scale(0.8); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>

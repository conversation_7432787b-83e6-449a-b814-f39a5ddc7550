<template>
  <div class="version-info">
    <!-- Version Badge -->
    <div 
      v-if="showBadge"
      class="fixed bottom-4 left-4 z-40 opacity-60 hover:opacity-100 transition-opacity duration-200"
      :class="{ 'hidden': !FEATURE_FLAGS.VERSION_DISPLAY }"
    >
      <div 
        class="badge badge-sm bg-base-300/80 text-base-content/70 border-0 backdrop-blur-sm cursor-pointer"
        @click="toggleModal"
        :title="getFullVersionInfo()"
      >
        v{{ getCurrentVersion() }}
      </div>
    </div>

    <!-- Version Modal -->
    <div 
      v-if="showModal" 
      class="modal modal-open"
      @click.self="closeModal"
    >
      <div class="modal-box max-w-2xl">
        <div class="flex items-center justify-between mb-6">
          <h3 class="font-bold text-lg flex items-center gap-2">
            <Icon name="info" size="md" />
            HLenergy Version Information
          </h3>
          <button 
            class="btn btn-sm btn-circle btn-ghost"
            @click="closeModal"
          >
            <Icon name="x" size="sm" />
          </button>
        </div>

        <!-- Current Version Info -->
        <div class="bg-base-200 rounded-lg p-4 mb-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 class="font-semibold text-sm text-base-content/70 mb-1">Version</h4>
              <p class="font-mono text-lg">{{ VERSION_INFO.version }}</p>
            </div>
            <div>
              <h4 class="font-semibold text-sm text-base-content/70 mb-1">Build</h4>
              <p class="font-mono">{{ VERSION_INFO.buildNumber }}</p>
            </div>
            <div>
              <h4 class="font-semibold text-sm text-base-content/70 mb-1">Environment</h4>
              <span 
                class="badge badge-sm"
                :class="{
                  'badge-success': VERSION_INFO.environment === 'production',
                  'badge-warning': VERSION_INFO.environment === 'staging',
                  'badge-info': VERSION_INFO.environment === 'development'
                }"
              >
                {{ VERSION_INFO.environment }}
              </span>
            </div>
            <div>
              <h4 class="font-semibold text-sm text-base-content/70 mb-1">Build Date</h4>
              <p class="text-sm">{{ formatDate(VERSION_INFO.buildDate) }}</p>
            </div>
          </div>
        </div>

        <!-- Socket.io Status Section -->
        <div class="bg-base-200 rounded-lg p-4 mb-6">
          <div class="flex items-center justify-between mb-3">
            <h4 class="font-semibold text-base-content">Real-time Connection</h4>
            <SocketStatus />
          </div>
          <p class="text-sm text-base-content/70">
            Live connection status for real-time updates and notifications.
          </p>
        </div>

        <!-- Features -->
        <div class="mb-6">
          <h4 class="font-semibold mb-3 flex items-center gap-2">
            <Icon name="star" size="sm" />
            Current Features
          </h4>
          <div class="flex flex-wrap gap-2">
            <span 
              v-for="feature in VERSION_INFO.features" 
              :key="feature"
              class="badge badge-outline badge-sm"
            >
              {{ feature }}
            </span>
          </div>
        </div>

        <!-- Latest Changelog -->
        <div v-if="latestChangelog" class="mb-6">
          <h4 class="font-semibold mb-3 flex items-center gap-2">
            <Icon name="clock" size="sm" />
            Latest Changes ({{ latestChangelog.version }})
          </h4>
          <div class="space-y-3">
            <div v-if="latestChangelog.changes.added?.length" class="text-sm">
              <h5 class="font-medium text-success mb-1">✨ Added</h5>
              <ul class="list-disc list-inside space-y-1 text-base-content/70 ml-4">
                <li v-for="item in latestChangelog.changes.added" :key="item">{{ item }}</li>
              </ul>
            </div>
            <div v-if="latestChangelog.changes.changed?.length" class="text-sm">
              <h5 class="font-medium text-info mb-1">🔄 Changed</h5>
              <ul class="list-disc list-inside space-y-1 text-base-content/70 ml-4">
                <li v-for="item in latestChangelog.changes.changed" :key="item">{{ item }}</li>
              </ul>
            </div>
            <div v-if="latestChangelog.changes.fixed?.length" class="text-sm">
              <h5 class="font-medium text-warning mb-1">🐛 Fixed</h5>
              <ul class="list-disc list-inside space-y-1 text-base-content/70 ml-4">
                <li v-for="item in latestChangelog.changes.fixed" :key="item">{{ item }}</li>
              </ul>
            </div>
            <div v-if="latestChangelog.changes.removed?.length" class="text-sm">
              <h5 class="font-medium text-error mb-1">🗑️ Removed</h5>
              <ul class="list-disc list-inside space-y-1 text-base-content/70 ml-4">
                <li v-for="item in latestChangelog.changes.removed" :key="item">{{ item }}</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Full Changelog Button -->
        <div class="flex justify-between items-center">
          <button 
            class="btn btn-outline btn-sm"
            @click="showFullChangelog = !showFullChangelog"
          >
            <Icon name="list" size="sm" />
            {{ showFullChangelog ? 'Hide' : 'Show' }} Full Changelog
          </button>
          <div class="text-xs text-base-content/50">
            Git: {{ VERSION_INFO.gitCommit?.substring(0, 7) || 'local' }}
          </div>
        </div>

        <!-- Full Changelog -->
        <div v-if="showFullChangelog" class="mt-6 max-h-64 overflow-y-auto">
          <div 
            v-for="entry in VERSION_INFO.changelog" 
            :key="entry.version"
            class="border-l-2 border-base-300 pl-4 pb-4 mb-4 last:mb-0"
          >
            <div class="flex items-center gap-2 mb-2">
              <span class="font-semibold">v{{ entry.version }}</span>
              <span class="text-xs text-base-content/50">{{ entry.date }}</span>
            </div>
            <div class="space-y-2 text-sm">
              <div v-if="entry.changes.added?.length">
                <span class="text-success font-medium">Added:</span>
                <ul class="list-disc list-inside ml-4 text-base-content/70">
                  <li v-for="item in entry.changes.added" :key="item">{{ item }}</li>
                </ul>
              </div>
              <div v-if="entry.changes.changed?.length">
                <span class="text-info font-medium">Changed:</span>
                <ul class="list-disc list-inside ml-4 text-base-content/70">
                  <li v-for="item in entry.changes.changed" :key="item">{{ item }}</li>
                </ul>
              </div>
              <div v-if="entry.changes.fixed?.length">
                <span class="text-warning font-medium">Fixed:</span>
                <ul class="list-disc list-inside ml-4 text-base-content/70">
                  <li v-for="item in entry.changes.fixed" :key="item">{{ item }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  VERSION_INFO, 
  getCurrentVersion, 
  getFullVersionInfo, 
  getLatestChangelog,
  FEATURE_FLAGS 
} from '@/config/version'
import Icon from '@/components/common/Icon.vue'
import SocketStatus from '@/components/common/SocketStatus.vue'

interface Props {
  showBadge?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showBadge: true
})

const showModal = ref(false)
const showFullChangelog = ref(false)

const latestChangelog = computed(() => getLatestChangelog())

const toggleModal = () => {
  showModal.value = !showModal.value
  showFullChangelog.value = false
}

const closeModal = () => {
  showModal.value = false
  showFullChangelog.value = false
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Keyboard shortcut to open version modal (Ctrl/Cmd + Shift + V)
const handleKeydown = (event: KeyboardEvent) => {
  if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'V') {
    event.preventDefault()
    toggleModal()
  }
}

// Add keyboard listener
if (typeof window !== 'undefined') {
  window.addEventListener('keydown', handleKeydown)
}
</script>

<style scoped>
.version-info {
  /* Ensure version badge doesn't interfere with other elements */
  pointer-events: none;
}

.version-info .badge,
.version-info .modal {
  pointer-events: auto;
}

/* Smooth animations */
.modal {
  backdrop-filter: blur(4px);
}

.modal-box {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>

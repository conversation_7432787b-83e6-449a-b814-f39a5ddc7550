<template>
  <header
    class="navbar bg-base-100/95 backdrop-blur-md shadow-lg border-b border-base-200/50 px-3 sm:px-6 sticky top-0 z-50 transition-all duration-300 ease-in-out"
    :class="{ '-translate-y-full': isHidden }"
  >
    <div class="navbar-start">
      <!-- Mobile Menu -->
      <div class="dropdown">
        <div tabindex="0" role="button" class="btn btn-ghost btn-square lg:hidden hover:bg-base-200 transition-colors duration-200">
          <Icon name="menu" size="md" />
        </div>
        <ul
          tabindex="0"
          class="menu menu-sm dropdown-content bg-base-100/95 backdrop-blur-md rounded-xl z-[1] mt-3 w-80 p-4 shadow-2xl border border-base-200/50"
        >
          <li class="menu-title mb-3">
            <span class="text-base-content opacity-80 font-semibold text-sm uppercase tracking-wider">{{ $t('navigation.menu') }}</span>
          </li>
          <li><RouterLink to="/" class="flex items-center gap-3 py-3 px-4 rounded-xl hover:bg-base-200/70 active:bg-base-300/50 transition-all duration-200">
            <Icon name="home" size="sm" class="flex-shrink-0 text-primary" />
            <span class="truncate font-medium">{{ $t('navigation.home') }}</span>
          </RouterLink></li>
          <li><RouterLink to="/about" class="flex items-center gap-3 py-3 px-4 rounded-xl hover:bg-base-200/70 active:bg-base-300/50 transition-all duration-200">
            <Icon name="info" size="sm" class="flex-shrink-0 text-primary" />
            <span class="truncate font-medium">{{ $t('navigation.about') }}</span>
          </RouterLink></li>
          <li><RouterLink to="/services" class="flex items-center gap-3 py-3 px-4 rounded-xl hover:bg-base-200/70 active:bg-base-300/50 transition-all duration-200">
            <Icon name="grid" size="sm" class="flex-shrink-0 text-primary" />
            <span class="truncate font-medium">{{ $t('navigation.services') }}</span>
          </RouterLink></li>
          <li><RouterLink to="/contact" class="flex items-center gap-3 py-3 px-4 rounded-xl hover:bg-base-200/70 active:bg-base-300/50 transition-all duration-200">
            <Icon name="message" size="sm" class="flex-shrink-0 text-primary" />
            <span class="truncate font-medium">{{ $t('navigation.contact') }}</span>
          </RouterLink></li>


          <!-- Authenticated Mobile Menu Items -->
          <template v-if="authStore.isAuthenticated">
            <li class="menu-title mt-6 mb-3">
              <span class="text-base-content opacity-80 font-semibold text-sm uppercase tracking-wider">{{ $t('navigation.account') }}</span>
            </li>
            <li><RouterLink to="/dashboard" class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-base-200 transition-colors">
              <Icon name="chart-bar" size="sm" class="flex-shrink-0" />
              <span class="truncate">{{ $t('navigation.dashboard') }}</span>
            </RouterLink></li>
            <li><RouterLink to="/profile" class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-base-200 transition-colors">
              <Icon name="user" size="sm" class="flex-shrink-0" />
              <span class="truncate">{{ $t('navigation.profile') }}</span>
            </RouterLink></li>
            <li v-if="authStore.isAdmin"><RouterLink to="/admin" class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-base-200 transition-colors">
              <Icon name="cog" size="sm" class="flex-shrink-0" />
              <span class="truncate">{{ $t('navigation.admin') }}</span>
            </RouterLink></li>
            <li><RouterLink to="/crm" class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-base-200 transition-colors">
              <Icon name="users" size="sm" class="flex-shrink-0" />
              <span class="truncate">{{ $t('navigation.crm') }}</span>
            </RouterLink></li>
          </template>

          <!-- Offline Status -->
          <li v-if="!isOnline" class="mt-4">
            <span class="flex items-center gap-3 py-3 px-4 text-warning bg-warning/10 rounded-lg">
              <Icon name="wifi" size="sm" />
              <span class="badge badge-warning badge-sm">{{ $t('common.offline') }}</span>
            </span>
          </li>

          <!-- Mobile Auth Actions -->
          <template v-if="!authStore.isAuthenticated">
            <li class="menu-title mt-4 mb-2">
              <span class="text-base-content opacity-70 font-semibold">{{ $t('navigation.account') }}</span>
            </li>
            <li><RouterLink to="/login" class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-base-200 transition-colors">
              <Icon name="login" size="sm" class="flex-shrink-0" />
              <span class="truncate">{{ $t('auth.login') }}</span>
            </RouterLink></li>
            <li><RouterLink to="/register" class="flex items-center gap-3 py-3 px-4 rounded-lg bg-primary text-primary-content hover:bg-primary-focus transition-colors">
              <Icon name="user-plus" size="sm" class="flex-shrink-0" />
              <span class="truncate">{{ $t('auth.register') }}</span>
            </RouterLink></li>
          </template>

          <!-- Mobile Logout -->
          <template v-else>
            <li class="mt-4">
              <a @click="handleLogout" class="flex items-center gap-3 py-3 px-4 rounded-lg text-error hover:bg-error/10 transition-colors">
                <Icon name="logout" size="sm" class="flex-shrink-0" />
                <span class="truncate">{{ $t('auth.logout') }}</span>
              </a>
            </li>
          </template>
        </ul>
      </div>
      <RouterLink to="/" class="btn btn-ghost hover:bg-base-200/70 px-2 sm:px-4 transition-all duration-200">
        <Logo size="sm" :show-text="true" class="hover:scale-105 transition-transform duration-300" />
      </RouterLink>
    </div>
    
    <div class="navbar-center hidden lg:flex">
      <ul class="menu menu-horizontal px-2 gap-1">
        <li><RouterLink to="/" class="px-4 py-2 rounded-lg font-medium hover:bg-base-200/70 transition-all duration-200">{{ $t('navigation.home') }}</RouterLink></li>
        <li><RouterLink to="/about" class="px-4 py-2 rounded-lg font-medium hover:bg-base-200/70 transition-all duration-200">{{ $t('navigation.about') }}</RouterLink></li>
        <li><RouterLink to="/services" class="px-4 py-2 rounded-lg font-medium hover:bg-base-200/70 transition-all duration-200">{{ $t('navigation.services') }}</RouterLink></li>
        <li><RouterLink to="/contact" class="px-4 py-2 rounded-lg font-medium hover:bg-base-200/70 transition-all duration-200">{{ $t('navigation.contact') }}</RouterLink></li>
        <li v-if="!isOnline">
          <span class="flex items-center gap-2 text-warning px-3 py-2">
            <Icon name="wifi" size="xs" />
            <span class="badge badge-warning badge-xs font-medium">{{ $t('common.offline') }}</span>
          </span>
        </li>
      </ul>
    </div>
    
    <div class="navbar-end gap-2 sm:gap-3">
      <!-- Theme Switcher -->
      <ThemeSwitcher />

      <!-- Language Selector -->
      <div class="dropdown dropdown-end">
        <div tabindex="0" role="button" class="btn btn-ghost btn-sm sm:btn-md gap-1 sm:gap-2 hover:bg-base-200/70 transition-all duration-200">
          <Icon name="globe" size="sm" class="text-primary" />
          <span class="hidden sm:inline text-sm font-medium">{{ currentLocale.toUpperCase() }}</span>
          <Icon name="chevron-down" size="xs" />
        </div>
        <ul tabindex="0" class="dropdown-content menu bg-base-100/95 backdrop-blur-md rounded-xl z-[1] w-40 p-2 shadow-2xl border border-base-200/50">
          <li><a @click="changeLocale('en')" class="flex items-center gap-3 py-2.5 px-3 hover:bg-base-200/70 rounded-lg transition-all duration-200 cursor-pointer">
            <span class="text-lg">🇺🇸</span>
            <span class="font-medium">English</span>
          </a></li>
          <li><a @click="changeLocale('es')" class="flex items-center gap-3 py-2.5 px-3 hover:bg-base-200/70 rounded-lg transition-all duration-200 cursor-pointer">
            <span class="text-lg">🇪🇸</span>
            <span class="font-medium">Español</span>
          </a></li>
          <li><a @click="changeLocale('pt')" class="flex items-center gap-3 py-2.5 px-3 hover:bg-base-200/70 rounded-lg transition-all duration-200 cursor-pointer">
            <span class="text-lg">🇵🇹</span>
            <span class="font-medium">Português</span>
          </a></li>
        </ul>
      </div>
      
      <!-- Authentication Section -->
      <div v-if="authStore.isAuthenticated" class="hidden lg:flex items-center gap-3 ml-4">
        <!-- Authenticated State -->
          <!-- User Menu Dropdown -->
          <div class="dropdown dropdown-end">
            <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar hover:bg-base-200/70 transition-all duration-200">
              <div class="w-10 h-10 rounded-full bg-gradient-to-br from-primary via-primary-focus to-secondary text-primary-content flex items-center justify-center font-bold shadow-lg border-2 border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-110">
                {{ authStore.userInitials }}
              </div>
            </div>
            <ul tabindex="0" class="dropdown-content menu bg-base-100/95 backdrop-blur-md rounded-xl z-[1] w-60 p-3 shadow-2xl border border-base-200/50">
              <li class="menu-title mb-2">
                <span class="text-sm font-semibold">{{ authStore.user?.name }}</span>
                <span class="text-xs opacity-70">{{ authStore.user?.email }}</span>
              </li>
              <li><RouterLink to="/dashboard" class="py-2 hover:bg-base-200 rounded-lg transition-colors">
                <Icon name="chart-bar" size="sm" />
                {{ $t('navigation.dashboard') }}
              </RouterLink></li>
              <li><RouterLink to="/profile" class="py-2 hover:bg-base-200 rounded-lg transition-colors">
                <Icon name="user" size="sm" />
                {{ $t('navigation.profile') }}
              </RouterLink></li>
              <li v-if="authStore.isAdmin"><RouterLink to="/admin" class="py-2 hover:bg-base-200 rounded-lg transition-colors">
                <Icon name="cog" size="sm" />
                {{ $t('navigation.admin') }}
              </RouterLink></li>
              <li><RouterLink to="/crm" class="py-2 hover:bg-base-200 rounded-lg transition-colors">
                <Icon name="users" size="sm" />
                {{ $t('navigation.crm') }}
              </RouterLink></li>
              <li><hr class="my-2"></li>
              <li><a @click="handleLogout" class="py-2 text-error hover:bg-error/10 rounded-lg transition-colors">
                <Icon name="logout" size="sm" />
                {{ $t('auth.logout') }}
              </a></li>
            </ul>
          </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { usePWA } from '@/composables/usePWA'
import Icon from '@/components/common/Icon.vue'
import Logo from '@/components/common/Logo.vue'

import ThemeSwitcher from '@/components/ThemeSwitcher.vue'

const { locale, t } = useI18n()
const router = useRouter()
const authStore = useAuthStore()
const { isOnline } = usePWA()

const currentLocale = computed(() => locale.value)

// Navbar scroll behavior
const isHidden = ref(false)
const lastScrollY = ref(0)
const scrollThreshold = 10 // Minimum scroll distance to trigger hide/show

const handleScroll = () => {
  const currentScrollY = window.scrollY

  // Don't hide navbar when at the top of the page
  if (currentScrollY < scrollThreshold) {
    isHidden.value = false
    lastScrollY.value = currentScrollY
    return
  }

  // Hide navbar when scrolling down, show when scrolling up
  if (currentScrollY > lastScrollY.value && currentScrollY > scrollThreshold) {
    isHidden.value = true
  } else if (currentScrollY < lastScrollY.value) {
    isHidden.value = false
  }

  lastScrollY.value = currentScrollY
}

const changeLocale = (newLocale: string) => {
  locale.value = newLocale
  localStorage.setItem('locale', newLocale)
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/')
    // Show success message (you can add a toast notification here)
  } catch (error) {
    console.error('Logout error:', error)
  }
}

// Load saved locale on component mount
const savedLocale = localStorage.getItem('locale')
if (savedLocale) {
  locale.value = savedLocale
}

// Setup scroll listener
onMounted(() => {
  window.addEventListener('scroll', handleScroll, { passive: true })
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.router-link-active {
  font-weight: 600;
  color: hsl(var(--p));
  background-color: hsl(var(--p) / 0.1);
  border-radius: 0.75rem;
}

.navbar {
  min-height: 4rem;
}

/* Enhanced hover effects */
.btn:hover {
  transform: translateY(-1px);
}

.dropdown-content {
  animation: fadeInUp 0.2s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile menu improvements */
@media (max-width: 1023px) {
  .dropdown-content {
    max-height: 80vh;
    overflow-y: auto;
  }
}

/* Smooth transitions for theme changes */
* {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
</style>

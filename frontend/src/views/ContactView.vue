<template>
  <div class="min-h-screen bg-base-100">


    <!-- Contact Form Section -->
    <section class="py-20 bg-base-100">
      <div class="max-w-7xl mx-auto px-6 lg:px-8">
        <div class="text-center mb-12">
          <h1 class="text-4xl md:text-5xl font-bold mb-4 text-base-content">{{ $t('contact.title') }}</h1>
          <p class="text-lg text-base-content/70 max-w-3xl mx-auto">{{ $t('contact.section_subtitle') }}</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
          <!-- Contact Form -->
          <div class="relative">
            <!-- Background decoration -->
            <div class="absolute -inset-4 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-3xl blur-xl opacity-30"></div>

            <div class="relative card bg-base-100/80 backdrop-blur-sm shadow-2xl border border-base-200/50 hover:shadow-3xl transition-all duration-500">
              <div class="card-body p-8">
                <div class="flex items-center gap-3 mb-8">
                  <div class="p-3 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl">
                    <Icon name="message" size="lg" class="text-primary" />
                  </div>
                  <h3 class="text-2xl font-bold text-base-content">{{ $t('contact.form_title') }}</h3>
                </div>

                <form @submit.prevent="submitForm" class="space-y-6">
                  <!-- Submission Progress -->
                  <div v-if="submissionState !== 'idle'" class="mb-6">
                    <!-- Progress Bar -->
                    <div v-if="submissionState === 'validating' || submissionState === 'submitting'" class="mb-4">
                      <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-base-content">
                          {{ submissionState === 'validating' ? $t('contact.progress.validating') : $t('contact.progress.submitting') }}
                        </span>
                        <span class="text-sm text-base-content/70">{{ submissionProgress }}%</span>
                      </div>
                      <div class="w-full bg-base-200 rounded-full h-2">
                        <div
                          class="bg-gradient-to-r from-primary to-primary-focus h-2 rounded-full transition-all duration-500 ease-out"
                          :style="{ width: `${submissionProgress}%` }"
                        ></div>
                      </div>
                    </div>

                    <!-- Success Message -->
                    <div v-if="submissionState === 'success' && submitMessage" class="alert alert-success shadow-lg border border-success/20 bg-success/10 animate-fade-in-up">
                      <div class="flex items-center gap-3">
                        <div class="p-2 bg-success/20 rounded-full">
                          <Icon name="check-circle" size="md" class="text-success animate-bounce" />
                        </div>
                        <div class="flex-1">
                          <div class="font-medium">{{ $t('contact.progress.success') }}</div>
                          <div class="text-sm opacity-80">{{ submitMessage }}</div>
                        </div>
                      </div>
                    </div>

                    <!-- Error Message -->
                    <div v-if="submissionState === 'error' && submitError" class="alert alert-error shadow-lg border border-error/20 bg-error/10 animate-fade-in-up">
                      <div class="flex items-center gap-3">
                        <div class="p-2 bg-error/20 rounded-full">
                          <Icon name="x-circle" size="md" class="text-error animate-pulse" />
                        </div>
                        <div class="flex-1">
                          <div class="font-medium">{{ $t('contact.progress.error') }}</div>
                          <div class="text-sm opacity-80">{{ submitError }}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-semibold text-base-content">{{ $t('contact.name') }}</span>
                      <span class="label-text-alt text-error">*</span>
                    </label>
                    <div class="relative">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Icon name="user" size="sm" class="text-base-content/40" />
                      </div>
                      <input
                        type="text"
                        v-model="form.name"
                        class="input input-bordered w-full pl-10 focus:input-primary transition-all duration-200"
                        :class="{ 'input-error': formErrors.name }"
                        :placeholder="$t('contact.name_placeholder')"
                        :disabled="isSubmitting"
                        @blur="validateForm"
                        required
                      />
                    </div>
                    <div v-if="formErrors.name" class="label">
                      <span class="label-text-alt text-error">{{ formErrors.name }}</span>
                    </div>
                  </div>

                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-semibold text-base-content">{{ $t('contact.email') }}</span>
                      <span class="label-text-alt text-error">*</span>
                    </label>
                    <div class="relative">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Icon name="email" size="sm" class="text-base-content/40" />
                      </div>
                      <input
                        type="email"
                        v-model="form.email"
                        class="input input-bordered w-full pl-10 focus:input-primary transition-all duration-200"
                        :class="{ 'input-error': formErrors.email }"
                        :placeholder="$t('contact.email_placeholder')"
                        :disabled="isSubmitting"
                        @blur="validateForm"
                        required
                      />
                    </div>
                    <div v-if="formErrors.email" class="label">
                      <span class="label-text-alt text-error">{{ formErrors.email }}</span>
                    </div>
                  </div>

                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-semibold text-base-content">{{ $t('contact.phone') }}</span>
                      <span class="label-text-alt text-base-content/60">{{ $t('contact.optional') }}</span>
                    </label>
                    <div class="flex gap-2">
                      <!-- Country Code Selector -->
                      <div class="dropdown">
                        <div tabindex="0" role="button" class="btn btn-outline min-w-fit">
                          <span class="text-lg">{{ countryCodes.find(c => c.code === form.countryCode)?.flag }}</span>
                          <span class="text-sm">{{ form.countryCode }}</span>
                          <Icon name="chevron-down" size="xs" />
                        </div>
                        <ul tabindex="0" class="dropdown-content menu bg-base-100/95 backdrop-blur-md rounded-xl z-[1] w-80 p-2 shadow-2xl border border-base-200/50 max-h-60 overflow-y-auto">
                          <li v-for="country in countryCodes" :key="country.code">
                            <a @click="form.countryCode = country.code" class="flex items-center gap-3 py-3 px-3 hover:bg-base-200/70 rounded-lg transition-colors duration-200 min-h-fit">
                              <span class="text-lg flex-shrink-0">{{ country.flag }}</span>
                              <div class="flex-1 min-w-0">
                                <div class="font-medium text-sm leading-tight truncate">{{ country.name }}</div>
                                <div class="text-xs opacity-70 mt-0.5">{{ country.code }}</div>
                              </div>
                              <div v-if="form.countryCode === country.code" class="flex-shrink-0">
                                <Icon name="check" size="sm" class="text-primary" />
                              </div>
                            </a>
                          </li>
                        </ul>
                      </div>

                      <!-- Phone Number Input -->
                      <div class="relative flex-1">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Icon name="phone" size="sm" class="text-base-content/40" />
                        </div>
                        <input
                          type="tel"
                          v-model="form.phone"
                          class="input input-bordered w-full pl-10 focus:input-primary transition-all duration-200"
                          :class="{ 'input-error': formErrors.phone }"
                          :placeholder="$t('contact.phone_placeholder')"
                          :disabled="isSubmitting"
                          @blur="validateForm"
                        />
                      </div>
                    </div>
                    <div v-if="formErrors.phone" class="label">
                      <span class="label-text-alt text-error">{{ formErrors.phone }}</span>
                    </div>
                  </div>

                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-semibold text-base-content">{{ $t('contact.message') }}</span>
                      <span class="label-text-alt text-error">*</span>
                    </label>
                    <textarea
                      v-model="form.message"
                      class="textarea textarea-bordered h-32 focus:textarea-primary transition-all duration-200 resize-none"
                      :class="{ 'textarea-error': formErrors.message }"
                      :placeholder="$t('contact.message_placeholder')"
                      :disabled="isSubmitting"
                      @blur="validateForm"
                      required
                    ></textarea>
                    <div class="label">
                      <span v-if="formErrors.message" class="label-text-alt text-error">{{ formErrors.message }}</span>
                      <span v-else class="label-text-alt text-base-content/60">{{ form.message.length }}/10 {{ $t('contact.min_chars') }}</span>
                    </div>
                  </div>

                  <div class="form-control mt-8">
                    <button
                      type="submit"
                      class="btn btn-lg w-full group shadow-lg hover:shadow-xl transition-all duration-300"
                      :class="{
                        'btn-primary': submissionState === 'idle',
                        'btn-info': submissionState === 'validating' || submissionState === 'submitting',
                        'btn-success': submissionState === 'success',
                        'btn-error': submissionState === 'error',
                        'loading': isSubmitting
                      }"
                      :disabled="isSubmitting || submissionState === 'success'"
                    >
                      <!-- Idle State -->
                      <template v-if="submissionState === 'idle'">
                        <Icon name="send" size="sm" class="mr-2 group-hover:scale-110 transition-transform" />
                        <span>{{ $t('contact.send_button') }}</span>
                      </template>

                      <!-- Validating State -->
                      <template v-else-if="submissionState === 'validating'">
                        <span class="loading loading-spinner loading-sm mr-2"></span>
                        <span>{{ $t('contact.progress.validating') }}</span>
                      </template>

                      <!-- Submitting State -->
                      <template v-else-if="submissionState === 'submitting'">
                        <span class="loading loading-dots loading-sm mr-2"></span>
                        <span>{{ $t('contact.progress.submitting') }}</span>
                      </template>

                      <!-- Success State -->
                      <template v-else-if="submissionState === 'success'">
                        <Icon name="check" size="sm" class="mr-2 animate-bounce" />
                        <span>{{ $t('contact.progress.sent') }}</span>
                      </template>

                      <!-- Error State -->
                      <template v-else-if="submissionState === 'error'">
                        <Icon name="refresh" size="sm" class="mr-2" />
                        <span>{{ $t('contact.progress.retry') }}</span>
                      </template>
                    </button>
                  </div>

                  <!-- CAPTCHA Verification -->
                  <div class="form-control">
                    <CaptchaVerification
                      ref="captchaRef"
                      v-model="captchaVerified"
                      @verified="handleCaptchaVerified"
                      @error="handleCaptchaError"
                    />
                    <div v-if="formErrors.captcha" class="label">
                      <span class="label-text-alt text-error">{{ formErrors.captcha }}</span>
                    </div>
                  </div>

                  <!-- Honeypot field for spam protection (hidden from users) -->
                  <div class="hidden">
                    <label for="website">Website (leave blank):</label>
                    <input
                      type="text"
                      id="website"
                      name="website"
                      v-model="form.honeypot"
                      tabindex="-1"
                      autocomplete="off"
                    />
                  </div>

                  <div class="text-center mt-4">
                    <p class="text-sm text-base-content/60">
                      {{ $t('contact.form_note') }}
                    </p>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="space-y-8">
            <!-- Contact Details Card -->
            <div class="card bg-gradient-to-br from-secondary/5 to-secondary/10 border border-secondary/20 shadow-xl hover:shadow-2xl transition-all duration-300">
              <div class="card-body p-8">
                <div class="flex items-center gap-3 mb-6">
                  <div class="p-3 bg-gradient-to-br from-secondary/20 to-secondary/10 rounded-xl">
                    <Icon name="info" size="lg" class="text-secondary" />
                  </div>
                  <h3 class="text-2xl font-bold text-base-content">{{ $t('contact.info_title') }}</h3>
                </div>

                <div class="space-y-6">
                  <div class="flex items-start gap-4 p-4 rounded-xl hover:bg-base-200/50 transition-colors duration-200">
                    <div class="flex-shrink-0 w-12 h-12 bg-primary/20 rounded-xl flex items-center justify-center">
                      <Icon name="email" size="md" class="text-primary" />
                    </div>
                    <div class="flex-grow">
                      <h4 class="font-semibold text-base-content mb-2">{{ $t('contact.email_label') }}</h4>
                      <p class="text-base-content/70 mb-3"><EMAIL></p>
                      <a href="mailto:<EMAIL>" class="btn btn-primary btn-sm">
                        <Icon name="email" size="sm" class="mr-2" />
                        {{ $t('contact.email_us') }}
                      </a>
                    </div>
                  </div>

                  <div class="flex items-start gap-4 p-4 rounded-xl hover:bg-base-200/50 transition-colors duration-200">
                    <div class="flex-shrink-0 w-12 h-12 bg-secondary/20 rounded-xl flex items-center justify-center">
                      <Icon name="phone" size="md" class="text-secondary" />
                    </div>
                    <div class="flex-grow">
                      <h4 class="font-semibold text-base-content mb-2">{{ $t('contact.phone_label') }}</h4>
                      <p class="text-base-content/70 mb-3">+****************</p>
                      <a href="tel:+15551234567" class="btn btn-secondary btn-sm">
                        <Icon name="phone" size="sm" class="mr-2" />
                        {{ $t('contact.call_now') }}
                      </a>
                    </div>
                  </div>

                  <div class="flex items-start gap-4 p-4 rounded-xl hover:bg-base-200/50 transition-colors duration-200">
                    <div class="flex-shrink-0 w-12 h-12 bg-accent/20 rounded-xl flex items-center justify-center">
                      <Icon name="map-pin" size="md" class="text-accent" />
                    </div>
                    <div class="flex-grow">
                      <h4 class="font-semibold text-base-content mb-1">{{ $t('contact.address_label') }}</h4>
                      <p class="text-base-content/70">{{ $t('contact.address') }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Business Hours Card -->
            <div class="card bg-gradient-to-br from-warning/5 to-warning/10 border border-warning/20 shadow-xl hover:shadow-2xl transition-all duration-300">
              <div class="card-body p-8">
                <div class="flex items-center gap-3 mb-6">
                  <div class="p-3 bg-gradient-to-br from-warning/20 to-warning/10 rounded-xl">
                    <Icon name="clock" size="lg" class="text-warning" />
                  </div>
                  <h3 class="text-2xl font-bold text-base-content">{{ $t('contact.hours_title') }}</h3>
                </div>

                <div class="space-y-4">
                  <div class="flex justify-between items-center p-3 rounded-lg hover:bg-base-200/50 transition-colors">
                    <span class="font-medium text-base-content">{{ $t('contact.weekdays') }}</span>
                    <span class="text-base-content/70">{{ $t('contact.weekdays_hours') }}</span>
                  </div>
                  <div class="flex justify-between items-center p-3 rounded-lg hover:bg-base-200/50 transition-colors">
                    <span class="font-medium text-base-content">{{ $t('contact.saturday') }}</span>
                    <span class="text-base-content/70">{{ $t('contact.saturday_hours') }}</span>
                  </div>
                  <div class="flex justify-between items-center p-3 rounded-lg hover:bg-base-200/50 transition-colors">
                    <span class="font-medium text-base-content">{{ $t('contact.sunday') }}</span>
                    <span class="text-base-content/70">{{ $t('contact.sunday_hours') }}</span>
                  </div>
                </div>

              </div>
            </div>

          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useOfflineStore } from '@/stores/offline'
import { usePWA } from '@/composables/usePWA'
import { useFormAnalytics, useBusinessAnalytics } from '@/composables/useAnalytics'
import Icon from '@/components/common/Icon.vue'
import CaptchaVerification from '@/components/common/CaptchaVerification.vue'

const form = reactive({
  name: '',
  email: '',
  phone: '',
  countryCode: '+351', // Portugal default
  message: '',
  honeypot: '' // Anti-spam honeypot field
})

const formErrors = reactive({
  name: '',
  email: '',
  phone: '',
  message: '',
  captcha: ''
})

const isSubmitting = ref(false)
const submitMessage = ref('')
const submitError = ref('')
const lastSubmissionTime = ref(0)
const captchaVerified = ref(false)
const captchaToken = ref('')
const captchaRef = ref()

// Enhanced response handling
const submissionState = ref<'idle' | 'validating' | 'submitting' | 'success' | 'error'>('idle')
const submissionProgress = ref(0)
const responseDetails = ref<any>(null)

// Country codes data
const countryCodes = [
  { code: '+351', name: 'Portugal', flag: '🇵🇹' },
  { code: '+34', name: 'Spain', flag: '🇪🇸' },
  { code: '+33', name: 'France', flag: '🇫🇷' },
  { code: '+49', name: 'Germany', flag: '🇩🇪' },
  { code: '+39', name: 'Italy', flag: '🇮🇹' },
  { code: '+44', name: 'United Kingdom', flag: '🇬🇧' },
  { code: '+1', name: 'United States', flag: '🇺🇸' },
  { code: '+55', name: 'Brazil', flag: '🇧🇷' },
  { code: '+86', name: 'China', flag: '🇨🇳' },
  { code: '+91', name: 'India', flag: '🇮🇳' },
  { code: '+31', name: 'Netherlands', flag: '🇳🇱' },
  { code: '+41', name: 'Switzerland', flag: '🇨🇭' },
  { code: '+43', name: 'Austria', flag: '🇦🇹' },
  { code: '+32', name: 'Belgium', flag: '🇧🇪' },
  { code: '+45', name: 'Denmark', flag: '🇩🇰' },
  { code: '+46', name: 'Sweden', flag: '🇸🇪' },
  { code: '+47', name: 'Norway', flag: '🇳🇴' },
  { code: '+358', name: 'Finland', flag: '🇫🇮' },
  { code: '+353', name: 'Ireland', flag: '🇮🇪' },
  { code: '+420', name: 'Czech Republic', flag: '🇨🇿' }
]

const { t } = useI18n()
const offlineStore = useOfflineStore()
const { isOnline } = usePWA()

// Analytics tracking
const formAnalytics = useFormAnalytics('contact_form')
const businessAnalytics = useBusinessAnalytics()

// Validation functions
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const validatePhone = (phone: string, countryCode: string): boolean => {
  if (!phone) return true // Phone is optional

  // Remove spaces, dashes, parentheses
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '')

  // Country-specific validation patterns
  const validationPatterns: Record<string, RegExp> = {
    '+351': /^[29][0-9]{8}$/, // Portugal: 9 digits starting with 2 or 9
    '+34': /^[67][0-9]{8}$/, // Spain: 9 digits starting with 6 or 7
    '+33': /^[1-9][0-9]{8}$/, // France: 9 digits
    '+49': /^[1-9][0-9]{10,11}$/, // Germany: 11-12 digits
    '+39': /^[3][0-9]{8,9}$/, // Italy: 9-10 digits starting with 3
    '+44': /^[7][0-9]{9}$/, // UK: 10 digits starting with 7
    '+1': /^[2-9][0-9]{9}$/, // US/Canada: 10 digits
    '+55': /^[1-9][0-9]{10}$/, // Brazil: 11 digits
    '+86': /^[1][0-9]{10}$/, // China: 11 digits starting with 1
    '+91': /^[6-9][0-9]{9}$/ // India: 10 digits starting with 6-9
  }

  const pattern = validationPatterns[countryCode]
  if (pattern) {
    return pattern.test(cleanPhone)
  }

  // Fallback: general international format (6-15 digits)
  return /^[0-9]{6,15}$/.test(cleanPhone)
}

const validateForm = (): boolean => {
  let isValid = true

  // Reset errors
  Object.keys(formErrors).forEach(key => {
    formErrors[key as keyof typeof formErrors] = ''
  })

  // Name validation
  if (!form.name.trim()) {
    formErrors.name = t('contact.validation.name_required')
    isValid = false
  } else if (form.name.trim().length < 2) {
    formErrors.name = t('contact.validation.name_min_length')
    isValid = false
  }

  // Email validation
  if (!form.email.trim()) {
    formErrors.email = t('contact.validation.email_required')
    isValid = false
  } else if (!validateEmail(form.email)) {
    formErrors.email = t('contact.validation.email_invalid')
    isValid = false
  }

  // Phone validation (optional but must be valid if provided)
  if (form.phone && !validatePhone(form.phone, form.countryCode)) {
    const countryName = countryCodes.find(c => c.code === form.countryCode)?.name || 'selected country'
    formErrors.phone = t('contact.validation.phone_invalid', { country: countryName })
    isValid = false
  }

  // Message validation
  if (!form.message.trim()) {
    formErrors.message = t('contact.validation.message_required')
    isValid = false
  } else if (form.message.trim().length < 10) {
    formErrors.message = t('contact.validation.message_min_length')
    isValid = false
  }

  // CAPTCHA validation
  if (!captchaVerified.value) {
    formErrors.captcha = t('contact.validation.recaptcha_required')
    isValid = false
  }

  return isValid
}

const submitForm = async () => {
  if (isSubmitting.value) return

  // Reset states
  submitError.value = ''
  submitMessage.value = ''
  responseDetails.value = null
  submissionState.value = 'validating'
  submissionProgress.value = 0

  // Check honeypot for spam protection
  if (form.honeypot) {
    console.warn('Spam detected: honeypot field filled')
    submitError.value = t('contact.validation.spam_detected')
    submissionState.value = 'error'
    return
  }

  // Rate limiting: prevent submissions within 30 seconds
  const now = Date.now()
  if (now - lastSubmissionTime.value < 30000) {
    submitError.value = t('contact.validation.rate_limit')
    submissionState.value = 'error'
    return
  }

  // Validate form before submission
  submissionProgress.value = 20
  if (!validateForm()) {
    submissionState.value = 'error'
    return
  }

  try {
    isSubmitting.value = true
    submissionState.value = 'submitting'
    submissionProgress.value = 40

    // Simulate progress updates
    submissionProgress.value = 60
    await new Promise(resolve => setTimeout(resolve, 500))

    submissionProgress.value = 80
    const result = await offlineStore.submitContactForm({
      name: form.name,
      email: form.email,
      phone: form.phone ? `${form.countryCode} ${form.phone}` : undefined,
      message: form.message,
      source: 'website',
      captchaToken: captchaToken.value
    })

    submissionProgress.value = 100
    submissionState.value = 'success'
    responseDetails.value = result

    // Handle different response scenarios
    if (result.offline) {
      submitMessage.value = isOnline.value
        ? t('contact.response.queued_online')
        : t('contact.response.saved_offline')
    } else if (result.success) {
      submitMessage.value = t('contact.response.success')
    } else {
      submitMessage.value = t('contact.response.submitted')
    }

    // Track successful form completion
    formAnalytics.trackFormComplete('contact_form')
    businessAnalytics.trackLeadGeneration('website', 'medium')
    businessAnalytics.trackServiceInquiry('general', 'contact_form')

    // Record submission time for rate limiting
    lastSubmissionTime.value = now

    // Reset form
    Object.assign(form, {
      name: '',
      email: '',
      phone: '',
      countryCode: '+351', // Reset to Portugal default
      message: '',
      honeypot: ''
    })

    // Reset CAPTCHA
    captchaVerified.value = false
    captchaToken.value = ''
    if (captchaRef.value) {
      captchaRef.value.reset()
    }

    // Reset submission state after delay
    setTimeout(() => {
      submissionState.value = 'idle'
      submissionProgress.value = 0
      submitMessage.value = ''
    }, 5000) // Reset after 5 seconds

    // Log success for demo purposes
    console.log('Contact form submitted successfully:', result)

  } catch (error: any) {
    submissionState.value = 'error'
    submissionProgress.value = 0

    // Enhanced error handling
    if (error.response) {
      // Server responded with error status
      const status = error.response.status
      const data = error.response.data

      switch (status) {
        case 400:
          submitError.value = t('contact.errors.validation_failed')
          break
        case 429:
          submitError.value = t('contact.errors.rate_limit_exceeded')
          break
        case 500:
          submitError.value = t('contact.errors.server_error')
          break
        default:
          submitError.value = t('contact.errors.submission_failed')
      }

      responseDetails.value = { error: data, status }
    } else if (error.request) {
      // Network error
      submitError.value = t('contact.errors.network_error')
      responseDetails.value = { error: 'Network error', type: 'network' }
    } else {
      // Other error
      submitError.value = error.message || t('contact.errors.unknown_error')
      responseDetails.value = { error: error.message, type: 'unknown' }
    }

    console.error('Contact form submission error:', error)
  } finally {
    isSubmitting.value = false

    // Reset progress after delay if there was an error
    if (submissionState.value === 'error') {
      setTimeout(() => {
        submissionProgress.value = 0
      }, 3000)
    }
  }
}

// CAPTCHA event handlers
const handleCaptchaVerified = (token: string) => {
  captchaToken.value = token
  formErrors.captcha = ''
  console.log('CAPTCHA verified:', token)
}

const handleCaptchaError = (error: string) => {
  captchaVerified.value = false
  captchaToken.value = ''
  formErrors.captcha = error
  console.error('CAPTCHA error:', error)
}

// Track form start when component mounts
onMounted(() => {
  formAnalytics.trackFormStart()
})
</script>

<style scoped>
/* Animation keyframes */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Animation delays */
.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

/* Enhanced shadow */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth transitions */
* {
  transition-property: transform, box-shadow, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Form focus states */
.input:focus,
.textarea:focus {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}
</style>

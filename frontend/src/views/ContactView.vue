<template>
  <div class="min-h-screen bg-base-100">
    <!-- Hero Section -->
    <section class="relative hero bg-gradient-to-br from-primary via-primary-focus to-secondary text-primary-content py-24 overflow-hidden">
      <!-- Background Pattern -->
      <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4xIj48cGF0aCBkPSJNMjAgMjBjMC01LjUtNC41LTEwLTEwLTEwcy0xMCA0LjUtMTAgMTAgNC41IDEwIDEwIDEwIDEwLTQuNSAxMC0xMHptMTAgMGMwLTUuNS00LjUtMTAtMTAtMTBzLTEwIDQuNS0xMCAxMCA0LjUgMTAgMTAgMTAgMTAtNC41IDEwLTEweiIvPjwvZz48L2c+PC9zdmc+')]"></div>
      </div>

      <div class="hero-content text-center relative z-10">
        <div class="max-w-5xl">
          <div class="mb-8 animate-fade-in-up">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full mb-6">
              <Icon name="message" size="2xl" class="text-white" />
            </div>
          </div>
          <h1 class="mb-6 text-5xl md:text-6xl font-bold animate-fade-in-up animation-delay-200">
            {{ $t('contact.title') }}
          </h1>
          <p class="mb-8 text-xl md:text-2xl leading-relaxed animate-fade-in-up animation-delay-400 max-w-4xl mx-auto">
            {{ $t('contact.hero_subtitle') }}
          </p>
          <div class="flex flex-wrap justify-center gap-4 animate-fade-in-up animation-delay-600">
            <div class="badge badge-lg bg-white/20 text-white border-white/30 backdrop-blur-sm">
              {{ $t('contact.badge_expert') }}
            </div>
            <div class="badge badge-lg bg-white/20 text-white border-white/30 backdrop-blur-sm">
              {{ $t('contact.badge_response') }}
            </div>
            <div class="badge badge-lg bg-white/20 text-white border-white/30 backdrop-blur-sm">
              {{ $t('contact.badge_consultation') }}
            </div>
          </div>
        </div>
      </div>

      <!-- Floating Elements -->
      <div class="absolute top-20 left-10 w-4 h-4 bg-white/20 rounded-full animate-float"></div>
      <div class="absolute top-40 right-20 w-6 h-6 bg-white/15 rounded-full animate-float animation-delay-1000"></div>
      <div class="absolute bottom-20 left-20 w-3 h-3 bg-white/25 rounded-full animate-float animation-delay-2000"></div>
    </section>

    <!-- Contact Form Section -->
    <section class="py-20 bg-gradient-to-br from-base-200/30 to-base-100">
      <div class="max-w-7xl mx-auto px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-bold mb-6 text-base-content">{{ $t('contact.section_title') }}</h2>
          <p class="text-xl text-base-content/70 max-w-4xl mx-auto leading-relaxed">{{ $t('contact.section_subtitle') }}</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
          <!-- Contact Form -->
          <div class="relative">
            <!-- Background decoration -->
            <div class="absolute -inset-4 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-3xl blur-xl opacity-30"></div>

            <div class="relative card bg-base-100/80 backdrop-blur-sm shadow-2xl border border-base-200/50 hover:shadow-3xl transition-all duration-500">
              <div class="card-body p-8">
                <div class="flex items-center gap-3 mb-8">
                  <div class="p-3 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl">
                    <Icon name="message" size="lg" class="text-primary" />
                  </div>
                  <h3 class="text-2xl font-bold text-base-content">{{ $t('contact.form_title') }}</h3>
                </div>

                <form @submit.prevent="submitForm" class="space-y-6">
                  <!-- Success Message -->
                  <div v-if="submitMessage" class="alert alert-success shadow-lg border border-success/20 bg-success/10">
                    <Icon name="check-circle" size="md" class="text-success" />
                    <span class="font-medium">{{ submitMessage }}</span>
                  </div>

                  <!-- Error Message -->
                  <div v-if="submitError" class="alert alert-error shadow-lg border border-error/20 bg-error/10">
                    <Icon name="x-circle" size="md" class="text-error" />
                    <span class="font-medium">{{ submitError }}</span>
                  </div>

                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-semibold text-base-content">{{ $t('contact.name') }}</span>
                      <span class="label-text-alt text-error">*</span>
                    </label>
                    <div class="relative">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Icon name="user" size="sm" class="text-base-content/40" />
                      </div>
                      <input
                        type="text"
                        v-model="form.name"
                        class="input input-bordered w-full pl-10 focus:input-primary transition-all duration-200"
                        :placeholder="$t('contact.name_placeholder')"
                        :disabled="isSubmitting"
                        required
                      />
                    </div>
                  </div>

                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-semibold text-base-content">{{ $t('contact.email') }}</span>
                      <span class="label-text-alt text-error">*</span>
                    </label>
                    <div class="relative">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Icon name="email" size="sm" class="text-base-content/40" />
                      </div>
                      <input
                        type="email"
                        v-model="form.email"
                        class="input input-bordered w-full pl-10 focus:input-primary transition-all duration-200"
                        :placeholder="$t('contact.email_placeholder')"
                        :disabled="isSubmitting"
                        required
                      />
                    </div>
                  </div>

                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-semibold text-base-content">{{ $t('contact.phone') }}</span>
                      <span class="label-text-alt text-base-content/60">{{ $t('contact.optional') }}</span>
                    </label>
                    <div class="relative">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Icon name="phone" size="sm" class="text-base-content/40" />
                      </div>
                      <input
                        type="tel"
                        v-model="form.phone"
                        class="input input-bordered w-full pl-10 focus:input-primary transition-all duration-200"
                        :placeholder="$t('contact.phone_placeholder')"
                        :disabled="isSubmitting"
                      />
                    </div>
                  </div>

                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-semibold text-base-content">{{ $t('contact.message') }}</span>
                      <span class="label-text-alt text-error">*</span>
                    </label>
                    <textarea
                      v-model="form.message"
                      class="textarea textarea-bordered h-32 focus:textarea-primary transition-all duration-200 resize-none"
                      :placeholder="$t('contact.message_placeholder')"
                      :disabled="isSubmitting"
                      required
                    ></textarea>
                  </div>

                  <div class="form-control mt-8">
                    <button
                      type="submit"
                      class="btn btn-primary btn-lg w-full group shadow-lg hover:shadow-xl transition-all duration-300"
                      :disabled="isSubmitting"
                      :class="{ 'loading': isSubmitting }"
                    >
                      <Icon v-if="!isSubmitting" name="send" size="sm" class="mr-2 group-hover:scale-110 transition-transform" />
                      <span v-if="!isSubmitting">{{ $t('contact.send_button') }}</span>
                      <span v-else class="loading loading-spinner loading-sm mr-2"></span>
                      <span v-if="isSubmitting">{{ $t('contact.sending') }}</span>
                    </button>
                  </div>

                  <div class="text-center mt-4">
                    <p class="text-sm text-base-content/60">
                      {{ $t('contact.form_note') }}
                    </p>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="space-y-8">
            <!-- Contact Details Card -->
            <div class="card bg-gradient-to-br from-secondary/5 to-secondary/10 border border-secondary/20 shadow-xl hover:shadow-2xl transition-all duration-300">
              <div class="card-body p-8">
                <div class="flex items-center gap-3 mb-6">
                  <div class="p-3 bg-gradient-to-br from-secondary/20 to-secondary/10 rounded-xl">
                    <Icon name="info" size="lg" class="text-secondary" />
                  </div>
                  <h3 class="text-2xl font-bold text-base-content">{{ $t('contact.info_title') }}</h3>
                </div>

                <div class="space-y-6">
                  <div class="flex items-start gap-4 p-4 rounded-xl hover:bg-base-200/50 transition-colors duration-200">
                    <div class="flex-shrink-0 w-12 h-12 bg-primary/20 rounded-xl flex items-center justify-center">
                      <Icon name="email" size="md" class="text-primary" />
                    </div>
                    <div class="flex-grow">
                      <h4 class="font-semibold text-base-content mb-1">{{ $t('contact.email_label') }}</h4>
                      <a href="mailto:<EMAIL>" class="text-primary hover:text-primary-focus transition-colors">
                        <EMAIL>
                      </a>
                    </div>
                  </div>

                  <div class="flex items-start gap-4 p-4 rounded-xl hover:bg-base-200/50 transition-colors duration-200">
                    <div class="flex-shrink-0 w-12 h-12 bg-secondary/20 rounded-xl flex items-center justify-center">
                      <Icon name="phone" size="md" class="text-secondary" />
                    </div>
                    <div class="flex-grow">
                      <h4 class="font-semibold text-base-content mb-1">{{ $t('contact.phone_label') }}</h4>
                      <a href="tel:+15551234567" class="text-secondary hover:text-secondary-focus transition-colors">
                        +****************
                      </a>
                    </div>
                  </div>

                  <div class="flex items-start gap-4 p-4 rounded-xl hover:bg-base-200/50 transition-colors duration-200">
                    <div class="flex-shrink-0 w-12 h-12 bg-accent/20 rounded-xl flex items-center justify-center">
                      <Icon name="map-pin" size="md" class="text-accent" />
                    </div>
                    <div class="flex-grow">
                      <h4 class="font-semibold text-base-content mb-1">{{ $t('contact.address_label') }}</h4>
                      <p class="text-base-content/70">{{ $t('contact.address') }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Business Hours Card -->
            <div class="card bg-gradient-to-br from-warning/5 to-warning/10 border border-warning/20 shadow-xl hover:shadow-2xl transition-all duration-300">
              <div class="card-body p-8">
                <div class="flex items-center gap-3 mb-6">
                  <div class="p-3 bg-gradient-to-br from-warning/20 to-warning/10 rounded-xl">
                    <Icon name="clock" size="lg" class="text-warning" />
                  </div>
                  <h3 class="text-2xl font-bold text-base-content">{{ $t('contact.hours_title') }}</h3>
                </div>

                <div class="space-y-4">
                  <div class="flex justify-between items-center p-3 rounded-lg hover:bg-base-200/50 transition-colors">
                    <span class="font-medium text-base-content">{{ $t('contact.weekdays') }}</span>
                    <span class="text-base-content/70">{{ $t('contact.weekdays_hours') }}</span>
                  </div>
                  <div class="flex justify-between items-center p-3 rounded-lg hover:bg-base-200/50 transition-colors">
                    <span class="font-medium text-base-content">{{ $t('contact.saturday') }}</span>
                    <span class="text-base-content/70">{{ $t('contact.saturday_hours') }}</span>
                  </div>
                  <div class="flex justify-between items-center p-3 rounded-lg hover:bg-base-200/50 transition-colors">
                    <span class="font-medium text-base-content">{{ $t('contact.sunday') }}</span>
                    <span class="text-base-content/70">{{ $t('contact.sunday_hours') }}</span>
                  </div>
                </div>

                <div class="mt-6 p-4 bg-info/10 border border-info/20 rounded-xl">
                  <div class="flex items-center gap-2 mb-2">
                    <Icon name="info" size="sm" class="text-info" />
                    <span class="font-semibold text-info">{{ $t('contact.emergency_title') }}</span>
                  </div>
                  <p class="text-sm text-base-content/70">{{ $t('contact.emergency_note') }}</p>
                </div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="card bg-gradient-to-br from-primary/5 to-primary/10 border border-primary/20 shadow-xl">
              <div class="card-body p-6">
                <h4 class="text-lg font-bold text-base-content mb-4">{{ $t('contact.quick_actions') }}</h4>
                <div class="grid grid-cols-1 gap-3">
                  <a href="tel:+15551234567" class="btn btn-outline btn-primary w-full justify-start">
                    <Icon name="phone" size="sm" class="mr-2" />
                    {{ $t('contact.call_now') }}
                  </a>
                  <a href="mailto:<EMAIL>" class="btn btn-outline btn-secondary w-full justify-start">
                    <Icon name="email" size="sm" class="mr-2" />
                    {{ $t('contact.email_us') }}
                  </a>
                  <RouterLink to="/services" class="btn btn-outline btn-accent w-full justify-start">
                    <Icon name="grid" size="sm" class="mr-2" />
                    {{ $t('contact.view_services') }}
                  </RouterLink>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { reactive, ref, onMounted } from 'vue'
import { useOfflineStore } from '@/stores/offline'
import { usePWA } from '@/composables/usePWA'
import { useFormAnalytics, useBusinessAnalytics } from '@/composables/useAnalytics'
import Icon from '@/components/common/Icon.vue'

const form = reactive({
  name: '',
  email: '',
  phone: '',
  message: ''
})

const isSubmitting = ref(false)
const submitMessage = ref('')
const submitError = ref('')

const offlineStore = useOfflineStore()
const { isOnline } = usePWA()

// Analytics tracking
const formAnalytics = useFormAnalytics('contact_form')
const businessAnalytics = useBusinessAnalytics()

const submitForm = async () => {
  if (isSubmitting.value) return

  try {
    isSubmitting.value = true
    submitError.value = ''
    submitMessage.value = ''

    const result = await offlineStore.submitContactForm({
      name: form.name,
      email: form.email,
      phone: form.phone || undefined,
      message: form.message,
      source: 'website'
    })

    if (result.offline) {
      submitMessage.value = isOnline.value
        ? 'Your message has been queued and will be sent shortly.'
        : 'Your message has been saved and will be sent when you\'re back online.'
    } else {
      submitMessage.value = 'Thank you for your message! We will get back to you soon.'
    }

    // Track successful form completion
    formAnalytics.trackFormComplete('contact_form')
    businessAnalytics.trackLeadGeneration('website', 'medium')
    businessAnalytics.trackServiceInquiry('general', 'contact_form')

    // Reset form
    Object.assign(form, {
      name: '',
      email: '',
      phone: '',
      message: ''
    })

    // Log success for demo purposes
    console.log('Contact form submitted successfully:', result)

  } catch (error: any) {
    submitError.value = error.message || 'Failed to submit form. Please try again.'
    console.error('Contact form submission error:', error)
  } finally {
    isSubmitting.value = false
  }
}

// Track form start when component mounts
onMounted(() => {
  formAnalytics.trackFormStart()
})
</script>

<style scoped>
/* Animation keyframes */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Animation delays */
.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

/* Enhanced shadow */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth transitions */
* {
  transition-property: transform, box-shadow, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Form focus states */
.input:focus,
.textarea:focus {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}
</style>

<template>
  <div class="min-h-screen bg-base-100">
    <!-- Hero Section -->
    <section class="relative hero bg-gradient-to-br from-primary via-primary-focus to-secondary text-primary-content py-24 overflow-hidden">
      <!-- Background Pattern -->
      <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4xIj48Y2lyY2xlIGN4PSIxMCIgY3k9IjEwIiByPSIyIi8+PGNpcmNsZSBjeD0iMzAiIGN5PSIxMCIgcj0iMiIvPjxjaXJjbGUgY3g9IjEwIiBjeT0iMzAiIHI9IjIiLz48Y2lyY2xlIGN4PSIzMCIgY3k9IjMwIiByPSIyIi8+PC9nPjwvZz48L3N2Zz4=')]"></div>
      </div>

      <div class="hero-content text-center relative z-10">
        <div class="max-w-5xl">
          <div class="mb-8 animate-fade-in-up">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full mb-6">
              <Icon name="grid" size="2xl" class="text-white" />
            </div>
          </div>
          <h1 class="mb-6 text-5xl md:text-6xl font-bold animate-fade-in-up animation-delay-200">
            {{ $t('services.title') }}
          </h1>
          <p class="mb-8 text-xl md:text-2xl leading-relaxed animate-fade-in-up animation-delay-400 max-w-4xl mx-auto">
            {{ $t('services.hero_subtitle') }}
          </p>
          <div class="flex flex-wrap justify-center gap-4 animate-fade-in-up animation-delay-600">
            <div class="badge badge-lg bg-white/20 text-white border-white/30 backdrop-blur-sm">
              {{ $t('services.badge_comprehensive') }}
            </div>
            <div class="badge badge-lg bg-white/20 text-white border-white/30 backdrop-blur-sm">
              {{ $t('services.badge_certified') }}
            </div>
            <div class="badge badge-lg bg-white/20 text-white border-white/30 backdrop-blur-sm">
              {{ $t('services.badge_results') }}
            </div>
          </div>
        </div>
      </div>

      <!-- Floating Elements -->
      <div class="absolute top-20 left-10 w-4 h-4 bg-white/20 rounded-full animate-float"></div>
      <div class="absolute top-40 right-20 w-6 h-6 bg-white/15 rounded-full animate-float animation-delay-1000"></div>
      <div class="absolute bottom-20 left-20 w-3 h-3 bg-white/25 rounded-full animate-float animation-delay-2000"></div>
    </section>

    <!-- Services Introduction -->
    <section class="py-20 bg-gradient-to-br from-base-200/30 to-base-100">
      <div class="max-w-7xl mx-auto px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-bold mb-6 text-base-content">{{ $t('services.section_title') }}</h2>
          <p class="text-xl text-base-content/70 max-w-4xl mx-auto leading-relaxed">{{ $t('services.section_subtitle') }}</p>
        </div>
      </div>
    </section>

    <!-- Services Grid -->
    <section class="py-20 bg-base-100">
      <div class="max-w-7xl mx-auto px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Energy Audit -->
          <div class="group">
            <div class="card bg-gradient-to-br from-primary/5 to-primary/10 border border-primary/20 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 h-full">
              <div class="card-body p-8">
                <div class="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-primary/30 to-primary/10 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Icon name="chart-bar" size="xl" class="text-primary" />
                </div>
                <h2 class="card-title text-2xl mb-4 text-base-content">{{ $t('services.energy_audit') }}</h2>
                <p class="text-base-content/70 leading-relaxed mb-6 flex-grow">{{ $t('services.energy_audit_desc') }}</p>
                <div class="space-y-3 mb-6">
                  <div class="flex items-center gap-2">
                    <Icon name="check" size="sm" class="text-primary flex-shrink-0" />
                    <span class="text-sm text-base-content/80">{{ $t('services.audit_feature_1') }}</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <Icon name="check" size="sm" class="text-primary flex-shrink-0" />
                    <span class="text-sm text-base-content/80">{{ $t('services.audit_feature_2') }}</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <Icon name="check" size="sm" class="text-primary flex-shrink-0" />
                    <span class="text-sm text-base-content/80">{{ $t('services.audit_feature_3') }}</span>
                  </div>
                </div>
                <div class="card-actions">
                  <RouterLink to="/contact" class="btn btn-primary w-full group-hover:btn-primary-focus transition-colors">
                    <Icon name="arrow-right" size="sm" class="mr-2" />
                    {{ $t('services.learn_more') }}
                  </RouterLink>
                </div>
              </div>
            </div>
          </div>

          <!-- Energy Consultation -->
          <div class="group">
            <div class="card bg-gradient-to-br from-secondary/5 to-secondary/10 border border-secondary/20 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 h-full">
              <div class="card-body p-8">
                <div class="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-secondary/30 to-secondary/10 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Icon name="lightbulb" size="xl" class="text-secondary" />
                </div>
                <h2 class="card-title text-2xl mb-4 text-base-content">{{ $t('services.consultation') }}</h2>
                <p class="text-base-content/70 leading-relaxed mb-6 flex-grow">{{ $t('services.consultation_desc') }}</p>
                <div class="space-y-3 mb-6">
                  <div class="flex items-center gap-2">
                    <Icon name="check" size="sm" class="text-secondary flex-shrink-0" />
                    <span class="text-sm text-base-content/80">{{ $t('services.consultation_feature_1') }}</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <Icon name="check" size="sm" class="text-secondary flex-shrink-0" />
                    <span class="text-sm text-base-content/80">{{ $t('services.consultation_feature_2') }}</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <Icon name="check" size="sm" class="text-secondary flex-shrink-0" />
                    <span class="text-sm text-base-content/80">{{ $t('services.consultation_feature_3') }}</span>
                  </div>
                </div>
                <div class="card-actions">
                  <RouterLink to="/contact" class="btn btn-secondary w-full group-hover:btn-secondary-focus transition-colors">
                    <Icon name="arrow-right" size="sm" class="mr-2" />
                    {{ $t('services.learn_more') }}
                  </RouterLink>
                </div>
              </div>
            </div>
          </div>

          <!-- Implementation Support -->
          <div class="group">
            <div class="card bg-gradient-to-br from-accent/5 to-accent/10 border border-accent/20 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 h-full">
              <div class="card-body p-8">
                <div class="flex items-center justify-center w-20 h-20 bg-gradient-to-br from-accent/30 to-accent/10 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  <Icon name="cog" size="xl" class="text-accent" />
                </div>
                <h2 class="card-title text-2xl mb-4 text-base-content">{{ $t('services.implementation') }}</h2>
                <p class="text-base-content/70 leading-relaxed mb-6 flex-grow">{{ $t('services.implementation_desc') }}</p>
                <div class="space-y-3 mb-6">
                  <div class="flex items-center gap-2">
                    <Icon name="check" size="sm" class="text-accent flex-shrink-0" />
                    <span class="text-sm text-base-content/80">{{ $t('services.implementation_feature_1') }}</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <Icon name="check" size="sm" class="text-accent flex-shrink-0" />
                    <span class="text-sm text-base-content/80">{{ $t('services.implementation_feature_2') }}</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <Icon name="check" size="sm" class="text-accent flex-shrink-0" />
                    <span class="text-sm text-base-content/80">{{ $t('services.implementation_feature_3') }}</span>
                  </div>
                </div>
                <div class="card-actions">
                  <RouterLink to="/contact" class="btn btn-accent w-full group-hover:btn-accent-focus transition-colors">
                    <Icon name="arrow-right" size="sm" class="mr-2" />
                    {{ $t('services.learn_more') }}
                  </RouterLink>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Additional Services Section -->
    <section class="py-20 bg-gradient-to-br from-base-200/30 to-base-100">
      <div class="max-w-7xl mx-auto px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-bold mb-6 text-base-content">{{ $t('services.additional_title') }}</h2>
          <p class="text-xl text-base-content/70 max-w-4xl mx-auto leading-relaxed">{{ $t('services.additional_subtitle') }}</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- Monitoring Service -->
          <div class="card bg-base-100/80 backdrop-blur-sm shadow-xl border border-base-200/50 hover:shadow-2xl transition-all duration-300">
            <div class="card-body p-8">
              <div class="flex items-center gap-4 mb-6">
                <div class="p-3 bg-gradient-to-br from-warning/20 to-warning/10 rounded-xl">
                  <Icon name="eye" size="lg" class="text-warning" />
                </div>
                <h3 class="text-2xl font-bold text-base-content">{{ $t('services.monitoring') }}</h3>
              </div>
              <p class="text-base-content/70 leading-relaxed mb-6">{{ $t('services.monitoring_desc') }}</p>
              <div class="flex flex-wrap gap-2">
                <div class="badge badge-warning badge-outline">{{ $t('services.monitoring_tag_1') }}</div>
                <div class="badge badge-warning badge-outline">{{ $t('services.monitoring_tag_2') }}</div>
                <div class="badge badge-warning badge-outline">{{ $t('services.monitoring_tag_3') }}</div>
              </div>
            </div>
          </div>

          <!-- Compliance Service -->
          <div class="card bg-base-100/80 backdrop-blur-sm shadow-xl border border-base-200/50 hover:shadow-2xl transition-all duration-300">
            <div class="card-body p-8">
              <div class="flex items-center gap-4 mb-6">
                <div class="p-3 bg-gradient-to-br from-info/20 to-info/10 rounded-xl">
                  <Icon name="shield-check" size="lg" class="text-info" />
                </div>
                <h3 class="text-2xl font-bold text-base-content">{{ $t('services.compliance') }}</h3>
              </div>
              <p class="text-base-content/70 leading-relaxed mb-6">{{ $t('services.compliance_desc') }}</p>
              <div class="flex flex-wrap gap-2">
                <div class="badge badge-info badge-outline">{{ $t('services.compliance_tag_1') }}</div>
                <div class="badge badge-info badge-outline">{{ $t('services.compliance_tag_2') }}</div>
                <div class="badge badge-info badge-outline">{{ $t('services.compliance_tag_3') }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-primary to-secondary text-primary-content">
      <div class="max-w-4xl mx-auto px-6 lg:px-8 text-center">
        <h2 class="text-4xl md:text-5xl font-bold mb-6">{{ $t('services.cta_title') }}</h2>
        <p class="text-xl mb-8 leading-relaxed">{{ $t('services.cta_subtitle') }}</p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <RouterLink to="/contact" class="btn btn-lg bg-white text-primary hover:bg-white/90 border-white">
            <Icon name="message" size="md" class="mr-2" />
            {{ $t('services.cta_contact') }}
          </RouterLink>
          <RouterLink to="/about" class="btn btn-lg btn-outline border-white text-white hover:bg-white hover:text-primary">
            <Icon name="info" size="md" class="mr-2" />
            {{ $t('services.cta_learn_more') }}
          </RouterLink>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import Icon from '@/components/common/Icon.vue'
</script>

<style scoped>
/* Animation keyframes */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Animation delays */
.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

/* Enhanced shadow */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth transitions */
* {
  transition-property: transform, box-shadow, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
</style>

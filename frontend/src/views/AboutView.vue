<template>
  <div class="min-h-screen bg-base-100">
    <!-- Hero Section -->
    <section class="relative hero bg-gradient-to-br from-primary via-primary-focus to-secondary text-primary-content py-24 overflow-hidden">
      <!-- Background Pattern -->
      <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
      </div>

      <div class="hero-content text-center relative z-10">
        <div class="max-w-4xl">
          <div class="mb-8 animate-fade-in-up">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full mb-6">
              <Icon name="info" size="2xl" class="text-white" />
            </div>
          </div>
          <h1 class="mb-6 text-5xl md:text-6xl font-bold animate-fade-in-up animation-delay-200">
            {{ $t('about.title') }}
          </h1>
          <p class="mb-8 text-xl md:text-2xl leading-relaxed animate-fade-in-up animation-delay-400 max-w-3xl mx-auto">
            {{ $t('about.hero_subtitle') }}
          </p>
          <div class="flex flex-wrap justify-center gap-4 animate-fade-in-up animation-delay-600">
            <div class="badge badge-lg bg-white/20 text-white border-white/30 backdrop-blur-sm">
              {{ $t('about.badge_experience') }}
            </div>
            <div class="badge badge-lg bg-white/20 text-white border-white/30 backdrop-blur-sm">
              {{ $t('about.badge_certified') }}
            </div>
            <div class="badge badge-lg bg-white/20 text-white border-white/30 backdrop-blur-sm">
              {{ $t('about.badge_clients') }}
            </div>
          </div>
        </div>
      </div>

      <!-- Floating Elements -->
      <div class="absolute top-20 left-10 w-4 h-4 bg-white/20 rounded-full animate-float"></div>
      <div class="absolute top-40 right-20 w-6 h-6 bg-white/15 rounded-full animate-float animation-delay-1000"></div>
      <div class="absolute bottom-20 left-20 w-3 h-3 bg-white/25 rounded-full animate-float animation-delay-2000"></div>
    </section>

    <!-- Mission & Vision Section -->
    <section class="py-20 bg-gradient-to-br from-base-200/30 to-base-100">
      <div class="max-w-7xl mx-auto px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div class="space-y-8">
            <div class="space-y-6">
              <div class="inline-flex items-center gap-3 px-4 py-2 bg-primary/10 rounded-full">
                <Icon name="target" size="sm" class="text-primary" />
                <span class="text-primary font-semibold">{{ $t('about.mission_label') }}</span>
              </div>
              <h2 class="text-4xl md:text-5xl font-bold text-base-content leading-tight">
                {{ $t('about.mission_title') }}
              </h2>
              <p class="text-lg text-base-content/80 leading-relaxed">
                {{ $t('about.mission_description') }}
              </p>
              <p class="text-lg text-base-content/80 leading-relaxed">
                {{ $t('about.mission_impact') }}
              </p>
            </div>

            <!-- Enhanced Stats -->
            <div class="grid grid-cols-2 gap-6">
              <div class="group">
                <div class="bg-gradient-to-br from-primary/10 to-primary/5 p-6 rounded-2xl border border-primary/20 hover:border-primary/40 transition-all duration-300 hover:shadow-lg">
                  <div class="flex items-center gap-3 mb-3">
                    <div class="p-2 bg-primary/20 rounded-lg">
                      <Icon name="check-circle" size="md" class="text-primary" />
                    </div>
                    <div class="stat-value text-2xl font-bold text-primary">500+</div>
                  </div>
                  <div class="stat-title text-sm font-medium text-base-content/70">{{ $t('about.stat_projects') }}</div>
                </div>
              </div>

              <div class="group">
                <div class="bg-gradient-to-br from-secondary/10 to-secondary/5 p-6 rounded-2xl border border-secondary/20 hover:border-secondary/40 transition-all duration-300 hover:shadow-lg">
                  <div class="flex items-center gap-3 mb-3">
                    <div class="p-2 bg-secondary/20 rounded-lg">
                      <Icon name="chart-bar" size="md" class="text-secondary" />
                    </div>
                    <div class="stat-value text-2xl font-bold text-secondary">30%</div>
                  </div>
                  <div class="stat-title text-sm font-medium text-base-content/70">{{ $t('about.stat_savings') }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Why Choose Us Card -->
          <div class="relative">
            <!-- Background decoration -->
            <div class="absolute -inset-4 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-3xl blur-xl opacity-30"></div>

            <div class="relative card bg-base-100/80 backdrop-blur-sm shadow-2xl border border-base-200/50 hover:shadow-3xl transition-all duration-500">
              <div class="card-body p-8">
                <div class="flex items-center gap-3 mb-6">
                  <div class="p-3 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl">
                    <Icon name="star" size="lg" class="text-primary" />
                  </div>
                  <h3 class="text-2xl font-bold text-base-content">{{ $t('about.why_choose_title') }}</h3>
                </div>

                <div class="space-y-4">
                  <div class="flex items-start gap-4 p-4 rounded-xl hover:bg-base-200/50 transition-colors duration-200">
                    <div class="flex-shrink-0 w-6 h-6 bg-success/20 rounded-full flex items-center justify-center mt-0.5">
                      <Icon name="check" size="sm" class="text-success" />
                    </div>
                    <div>
                      <h4 class="font-semibold text-base-content mb-1">{{ $t('about.feature_certified_title') }}</h4>
                      <p class="text-sm text-base-content/70">{{ $t('about.feature_certified_desc') }}</p>
                    </div>
                  </div>

                  <div class="flex items-start gap-4 p-4 rounded-xl hover:bg-base-200/50 transition-colors duration-200">
                    <div class="flex-shrink-0 w-6 h-6 bg-success/20 rounded-full flex items-center justify-center mt-0.5">
                      <Icon name="check" size="sm" class="text-success" />
                    </div>
                    <div>
                      <h4 class="font-semibold text-base-content mb-1">{{ $t('about.feature_customized_title') }}</h4>
                      <p class="text-sm text-base-content/70">{{ $t('about.feature_customized_desc') }}</p>
                    </div>
                  </div>

                  <div class="flex items-start gap-4 p-4 rounded-xl hover:bg-base-200/50 transition-colors duration-200">
                    <div class="flex-shrink-0 w-6 h-6 bg-success/20 rounded-full flex items-center justify-center mt-0.5">
                      <Icon name="check" size="sm" class="text-success" />
                    </div>
                    <div>
                      <h4 class="font-semibold text-base-content mb-1">{{ $t('about.feature_proven_title') }}</h4>
                      <p class="text-sm text-base-content/70">{{ $t('about.feature_proven_desc') }}</p>
                    </div>
                  </div>

                  <div class="flex items-start gap-4 p-4 rounded-xl hover:bg-base-200/50 transition-colors duration-200">
                    <div class="flex-shrink-0 w-6 h-6 bg-success/20 rounded-full flex items-center justify-center mt-0.5">
                      <Icon name="check" size="sm" class="text-success" />
                    </div>
                    <div>
                      <h4 class="font-semibold text-base-content mb-1">{{ $t('about.feature_support_title') }}</h4>
                      <p class="text-sm text-base-content/70">{{ $t('about.feature_support_desc') }}</p>
                    </div>
                  </div>
                </div>

                <div class="mt-8">
                  <RouterLink to="/contact" class="btn btn-primary btn-lg w-full group">
                    <Icon name="message" size="sm" class="mr-2 group-hover:scale-110 transition-transform" />
                    {{ $t('about.contact_cta') }}
                  </RouterLink>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Team & Values Section -->
    <section class="py-20 bg-base-100">
      <div class="max-w-7xl mx-auto px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-bold mb-6 text-base-content">{{ $t('about.values_title') }}</h2>
          <p class="text-xl text-base-content/70 max-w-3xl mx-auto">{{ $t('about.values_subtitle') }}</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div class="text-center group">
            <div class="w-20 h-20 bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
              <Icon name="shield-check" size="xl" class="text-primary" />
            </div>
            <h3 class="text-xl font-bold mb-3 text-base-content">{{ $t('about.value_integrity_title') }}</h3>
            <p class="text-base-content/70">{{ $t('about.value_integrity_desc') }}</p>
          </div>

          <div class="text-center group">
            <div class="w-20 h-20 bg-gradient-to-br from-secondary/20 to-secondary/10 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
              <Icon name="lightbulb" size="xl" class="text-secondary" />
            </div>
            <h3 class="text-xl font-bold mb-3 text-base-content">{{ $t('about.value_innovation_title') }}</h3>
            <p class="text-base-content/70">{{ $t('about.value_innovation_desc') }}</p>
          </div>

          <div class="text-center group">
            <div class="w-20 h-20 bg-gradient-to-br from-accent/20 to-accent/10 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
              <Icon name="leaf" size="xl" class="text-accent" />
            </div>
            <h3 class="text-xl font-bold mb-3 text-base-content">{{ $t('about.value_sustainability_title') }}</h3>
            <p class="text-base-content/70">{{ $t('about.value_sustainability_desc') }}</p>
          </div>

          <div class="text-center group">
            <div class="w-20 h-20 bg-gradient-to-br from-warning/20 to-warning/10 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
              <Icon name="heart" size="xl" class="text-warning" />
            </div>
            <h3 class="text-xl font-bold mb-3 text-base-content">{{ $t('about.value_excellence_title') }}</h3>
            <p class="text-base-content/70">{{ $t('about.value_excellence_desc') }}</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import Icon from '@/components/common/Icon.vue'
</script>

<style scoped>
/* Animation keyframes */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Animation delays */
.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

/* Enhanced shadow */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth transitions */
* {
  transition-property: transform, box-shadow, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
</style>
